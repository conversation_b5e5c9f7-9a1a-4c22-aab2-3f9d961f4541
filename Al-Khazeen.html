<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المخزون الاستراتيجي - معزز بالذكاء الاصطناعي</title>
    
    <!-- Application Favicon -->
    <link rel="icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Crect x='15' y='50' width='70' height='40' fill='%233B82F6' stroke='%232563EB' stroke-width='5' rx='5'/%3E%3Crect x='25' y='10' width='50' height='40' fill='%2360A5FA' stroke='%232563EB' stroke-width='5' rx='5'/%3E%3C/svg%3E">

    <!-- Google Fonts for Cairo -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Chart.js for beautiful charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        /* Custom styles to apply the Cairo font and other tweaks */
        body, input, button, select, textarea {
            font-family: 'Cairo', sans-serif;
        }
        .window-shadow {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .title-bar-buttons span {
            display: block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #a8a8a8;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        .sidebar-icon {
            width: 20px;
            text-align: center;
        }
        /* Style for the modal */
        .modal-backdrop {
            background-color: rgba(0,0,0,0.5);
            transition: opacity 0.3s ease;
        }
        .modal-content {
            transition: transform 0.3s ease;
        }
        .stat-card {
            border-right: 4px solid;
        }
        /* Sidebar active link style */
        .sidebar-link.active {
            background-color: #334155; /* slate-700 */
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body class="p-4 sm:p-8 flex items-center justify-center min-h-screen bg-slate-200">

    <!-- Desktop Application Window Frame -->
    <div class="w-full max-w-7xl h-[800px] bg-white rounded-xl window-shadow flex flex-col overflow-hidden">

        <!-- Title Bar -->
        <div class="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-slate-100 rounded-t-lg border-b border-slate-300">
            <div class="flex items-center gap-2 title-bar-buttons">
                <span class="bg-red-500"></span>
                <span class="bg-yellow-500"></span>
                <span class="bg-green-500"></span>
            </div>
            <div class="text-sm text-slate-700 font-bold">
                نظام المخزون الاستراتيجي - الجمعية التعاونية
            </div>
            <div class="w-16"></div> <!-- Spacer -->
        </div>

        <!-- Main Application Layout (Sidebar + Content) -->
        <div class="flex flex-grow h-full">
            
            <!-- Sidebar -->
            <div class="w-64 bg-slate-800 text-white p-5 flex-shrink-0 flex flex-col">
                <div class="flex items-center gap-3 pb-6 border-b border-slate-700">
                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fa-solid fa-user text-2xl"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-lg">مدير النظام</h3>
                        <p class="text-xs text-slate-400">جمعية المنقف التعاونية</p>
                    </div>
                </div>
                <nav id="sidebar-nav" class="mt-8 flex-grow">
                    <ul>
                        <li><a href="#" id="inventory-link" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-boxes-stacked sidebar-icon"></i> إدارة المخزون</a></li>
                        <li><a href="#" id="dashboard-link" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-chart-pie sidebar-icon"></i> لوحة التحكم</a></li>
                        <li><a href="#" id="suppliers-link" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-truck sidebar-icon"></i> الموردون</a></li>
                        <li><a href="#" id="reports-link" class="sidebar-link flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-file-invoice sidebar-icon"></i> التقارير</a></li>
                    </ul>
                </nav>
                <div class="flex-shrink-0">
                    <a href="#" class="flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-gear sidebar-icon"></i> الإعدادات</a>
                    <a href="#" class="flex items-center gap-3 px-4 py-3 rounded-md hover:bg-slate-700 text-slate-300"><i class="fa-solid fa-right-from-bracket sidebar-icon"></i> تسجيل الخروج</a>
                </div>
            </div>

            <!-- Content Area -->
            <div id="content-area" class="flex-grow bg-slate-50 p-6 md:p-8 overflow-y-auto">
                <!-- Content will be rendered here by JavaScript -->
            </div>
        </div>
    </div>
    
    <!-- Gemini Modal -->
    <div id="gemini-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 modal-backdrop hidden opacity-0">
        <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl modal-content transform scale-95">
             <div id="modal-content-wrapper">
                <!-- Content will be populated by JavaScript -->
             </div>
        </div>
    </div>

<script>
    // --- SAMPLE DATA ---
    let inventoryData = [
        { id: 1, name: 'أرز بسمتي (10 كج)', quantity: 85, reorderPoint: 100, unit: 'كيس', value: 10 },
        { id: 2, name: 'زيت طبخ (1.8 لتر)', quantity: 250, reorderPoint: 200, unit: 'علبة', value: 1.5 },
        { id: 3, name: 'سكر أبيض (5 كج)', quantity: 45, reorderPoint: 70, unit: 'كيس', value: 3 },
        { id: 4, name: 'حليب مجفف (2.5 كج)', quantity: 30, reorderPoint: 50, unit: 'علبة', value: 8 },
        { id: 5, name: 'معجون طماطم (8 علب)', quantity: 150, reorderPoint: 150, unit: 'حزمة', value: 2 },
        { id: 6, name: 'عدس أحمر (1 كج)', quantity: 200, reorderPoint: 120, unit: 'كيس', value: 1 },
        { id: 7, name: 'تونة (علبة)', quantity: 98, reorderPoint: 400, unit: 'علبة', value: 0.5 },
    ];
    let suppliersData = [
        { id: 1, code: 'SPL-001', name: 'شركة المطاحن الكويتية' },
        { id: 2, code: 'SPL-002', name: 'الشركة المتحدة للألبان' },
        { id: 3, code: 'SPL-003', name: 'مجموعة العليان للمواد الغذائية' },
    ];

    const contentArea = document.getElementById('content-area');
    const geminiModal = document.getElementById('gemini-modal');
    const modalContentWrapper = document.getElementById('modal-content-wrapper');

    // --- NAVIGATION LOGIC ---
    const navLinks = document.querySelectorAll('.sidebar-link');
    function setActiveLink(activeLink) {
        navLinks.forEach(link => link.classList.remove('active'));
        activeLink.classList.add('active');
    }

    document.getElementById('inventory-link').addEventListener('click', (e) => { e.preventDefault(); setActiveLink(e.currentTarget); renderInventory(); });
    document.getElementById('dashboard-link').addEventListener('click', (e) => { e.preventDefault(); setActiveLink(e.currentTarget); renderDashboard(); });
    document.getElementById('suppliers-link').addEventListener('click', (e) => { e.preventDefault(); setActiveLink(e.currentTarget); renderSuppliers(); });
    
    // --- PAGE RENDERERS ---

    function renderInventory() {
        contentArea.innerHTML = `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-slate-800">إدارة المخزون</h1>
            </div>
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <table class="w-full text-sm text-left text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-100">
                        <tr>
                            <th scope="col" class="px-6 py-3">الصنف</th>
                            <th scope="col" class="px-6 py-3">الكمية الحالية</th>
                            <th scope="col" class="px-6 py-3">حد الطلب</th>
                            <th scope="col" class="px-6 py-3">الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="inventory-table-body">
                        ${inventoryData.map(item => {
                            const isLowStock = item.quantity <= item.reorderPoint;
                            const statusClass = isLowStock ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';
                            const statusText = isLowStock ? 'يتطلب إعادة طلب' : 'متوفر';
                            return `<tr class="bg-white border-b hover:bg-gray-50">
                                <th scope="row" class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap">${item.name}</th>
                                <td class="px-6 py-4">${item.quantity} ${item.unit}</td>
                                <td class="px-6 py-4">${item.reorderPoint} ${item.unit}</td>
                                <td class="px-6 py-4"><span class="px-2 py-1 font-semibold leading-tight text-xs rounded-full ${statusClass}">${statusText}</span></td>
                            </tr>`;
                        }).join('')}
                    </tbody>
                </table>
            </div>`;
    }

    function renderDashboard() {
        const totalValue = inventoryData.reduce((sum, item) => sum + (item.quantity * item.value), 0);
        const itemsToReorder = inventoryData.filter(item => item.quantity <= item.reorderPoint).length;
        const totalItems = inventoryData.length;
        const healthyStockPercentage = Math.round(((totalItems - itemsToReorder) / totalItems) * 100);
        contentArea.innerHTML = `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-slate-800">لوحة التحكم الرئيسية</h1>
                <button id="gemini-inventory-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2 transition-all duration-300">
                    <i class="fa-solid fa-lightbulb"></i><span>✨ توليد توصيات للمخزون</span>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow stat-card border-r-blue-500"><div class="flex items-center"><div class="bg-blue-100 text-blue-600 rounded-full h-12 w-12 flex items-center justify-center"><i class="fas fa-dollar-sign text-2xl"></i></div><div class="ms-4"><p class="text-sm font-medium text-gray-500">قيمة المخزون</p><p class="text-2xl font-bold text-gray-800">${totalValue.toLocaleString('ar-KW')} د.ك</p></div></div></div>
                <div class="bg-white p-6 rounded-lg shadow stat-card border-r-red-500"><div class="flex items-center"><div class="bg-red-100 text-red-600 rounded-full h-12 w-12 flex items-center justify-center"><i class="fas fa-exclamation-triangle text-2xl"></i></div><div class="ms-4"><p class="text-sm font-medium text-gray-500">أصناف تحتاج طلب</p><p class="text-2xl font-bold text-gray-800">${itemsToReorder}</p></div></div></div>
                <div class="bg-white p-6 rounded-lg shadow stat-card border-r-green-500"><div class="flex items-center"><div class="bg-green-100 text-green-600 rounded-full h-12 w-12 flex items-center justify-center"><i class="fas fa-shield-halved text-2xl"></i></div><div class="ms-4"><p class="text-sm font-medium text-gray-500">مخزون آمن</p><p class="text-2xl font-bold text-gray-800">${healthyStockPercentage}%</p></div></div></div>
                <div class="bg-white p-6 rounded-lg shadow stat-card border-r-yellow-500"><div class="flex items-center"><div class="bg-yellow-100 text-yellow-600 rounded-full h-12 w-12 flex items-center justify-center"><i class="fas fa-box-open text-2xl"></i></div><div class="ms-4"><p class="text-sm font-medium text-gray-500">إجمالي الأصناف</p><p class="text-2xl font-bold text-gray-800">${totalItems}</p></div></div></div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <div class="lg:col-span-3 bg-white p-6 rounded-lg shadow"><h3 class="font-bold text-lg mb-4 text-slate-700">الأصناف الأقل مخزوناً</h3><canvas id="lowStockChart"></canvas></div>
                <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow"><h3 class="font-bold text-lg mb-4 text-slate-700">حالة المخزون</h3><canvas id="stockStatusChart"></canvas></div>
            </div>
        `;
        document.getElementById('gemini-inventory-btn').addEventListener('click', handleGenerateInventorySuggestions);
        renderLowStockChart();
        renderStockStatusChart();
    }

    function renderSuppliers() {
        contentArea.innerHTML = `
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-slate-800">إدارة الموردين</h1>
                <button id="gemini-supplier-btn" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg flex items-center gap-2 transition-all duration-300">
                    <i class="fa-solid fa-wand-magic-sparkles"></i><span>✨ اقتراح موردين جدد</span>
                </button>
            </div>
            <div class="bg-white p-6 rounded-lg shadow mb-8">
                 <h3 class="font-bold text-lg mb-4 text-slate-700">إضافة مورد جديد</h3>
                 <form id="add-supplier-form" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                    <div>
                        <label for="supplier-code" class="block text-sm font-medium text-gray-700 mb-1">كود المورد</label>
                        <input type="text" id="supplier-code" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                    </div>
                     <div>
                        <label for="supplier-name" class="block text-sm font-medium text-gray-700 mb-1">اسم المورد</label>
                        <input type="text" id="supplier-name" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                    </div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg flex items-center justify-center gap-2 h-11"><i class="fa-solid fa-save"></i><span>حفظ</span></button>
                 </form>
            </div>
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <table class="w-full text-sm text-left text-gray-500"><thead class="text-xs text-gray-700 uppercase bg-gray-100"><tr><th scope="col" class="px-6 py-3">#</th><th scope="col" class="px-6 py-3">كود المورد</th><th scope="col" class="px-6 py-3">اسم المورد</th><th scope="col" class="px-6 py-3">الإجراءات</th></tr></thead><tbody id="suppliers-table-body"></tbody></table>
            </div>`;
        renderSuppliersTable();
        document.getElementById('add-supplier-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const codeInput = document.getElementById('supplier-code');
            const nameInput = document.getElementById('supplier-name');
            const newSupplier = { id: suppliersData.length + 1, code: codeInput.value, name: nameInput.value };
            suppliersData.push(newSupplier);
            renderSuppliersTable();
            codeInput.value = ''; nameInput.value = '';
        });
        document.getElementById('gemini-supplier-btn').addEventListener('click', handleSuggestNewSuppliers);
    }

    function renderSuppliersTable() {
        const tableBody = document.getElementById('suppliers-table-body');
        tableBody.innerHTML = '';
        suppliersData.forEach((supplier, index) => {
            tableBody.innerHTML += `<tr class="bg-white border-b hover:bg-gray-50"><td class="px-6 py-4 font-medium text-gray-900">${index + 1}</td><td class="px-6 py-4">${supplier.code}</td><td class="px-6 py-4 font-medium text-gray-900">${supplier.name}</td><td class="px-6 py-4"><button class="text-blue-600 hover:text-blue-800 font-semibold mr-4"><i class="fas fa-edit"></i> تعديل</button><button class="text-red-600 hover:text-red-800 font-semibold"><i class="fas fa-trash"></i> حذف</button></td></tr>`;
        });
    }

    // --- CHART RENDERING ---
    function renderLowStockChart() { const ctx = document.getElementById('lowStockChart').getContext('2d'); const itemsWithStockRatio = inventoryData.map(item => ({ name: item.name, ratio: (item.quantity / item.reorderPoint) * 100 })).sort((a, b) => a.ratio - b.ratio).slice(0, 5); new Chart(ctx, { type: 'bar', data: { labels: itemsWithStockRatio.map(item => item.name), datasets: [{ label: 'نسبة المخزون من حد الطلب (%)', data: itemsWithStockRatio.map(item => item.ratio.toFixed(2)), backgroundColor: 'rgba(239, 68, 68, 0.6)', borderColor: 'rgba(239, 68, 68, 1)', borderWidth: 1 }] }, options: { indexAxis: 'y', scales: { x: { beginAtZero: true, max: 120 } }, responsive: true, plugins: { legend: { display: false } } } }); }
    function renderStockStatusChart() { const ctx = document.getElementById('stockStatusChart').getContext('2d'); const statusCounts = { ok: 0, reorder: 0, critical: 0 }; inventoryData.forEach(item => { const ratio = item.quantity / item.reorderPoint; if (ratio <= 0.75) { statusCounts.critical++; } else if (ratio <= 1) { statusCounts.reorder++; } else { statusCounts.ok++; } }); new Chart(ctx, { type: 'doughnut', data: { labels: ['مخزون آمن', 'يتطلب إعادة طلب', 'مخزون حرج'], datasets: [{ label: 'حالة المخزون', data: [statusCounts.ok, statusCounts.reorder, statusCounts.critical], backgroundColor: ['rgba(34, 197, 94, 0.7)', 'rgba(251, 146, 60, 0.7)', 'rgba(239, 68, 68, 0.7)'], borderColor: ['rgba(34, 197, 94, 1)', 'rgba(251, 146, 60, 1)', 'rgba(239, 68, 68, 1)'], borderWidth: 1 }] }, options: { responsive: true, plugins: { legend: { position: 'top' } } } }); }

    // --- MODAL & GEMINI API ---
    function toggleModal() { if (geminiModal.classList.contains('hidden')) { geminiModal.classList.remove('hidden'); setTimeout(() => geminiModal.classList.remove('opacity-0'), 10); setTimeout(() => geminiModal.querySelector('.modal-content').classList.remove('scale-95'), 10); } else { geminiModal.classList.add('opacity-0'); geminiModal.querySelector('.modal-content').classList.add('scale-95'); setTimeout(() => geminiModal.classList.add('hidden'), 300); } }

    function showModalContent(html) { modalContentWrapper.innerHTML = html; document.getElementById('close-modal-btn')?.addEventListener('click', toggleModal); toggleModal(); }

    // Gemini Feature 1: Inventory Suggestions
    function handleGenerateInventorySuggestions() {
        const loadingHtml = `<div class="p-5 border-b flex justify-between items-center"><h3 class="text-2xl font-bold text-slate-800 flex items-center gap-3"><i class="fa-solid fa-lightbulb text-blue-500"></i><span>توصيات المخزون</span></h3><button id="close-modal-btn" class="text-slate-400 hover:text-slate-700"><i class="fa-solid fa-times text-2xl"></i></button></div><div class="p-6"><div class="text-center p-8"><i class="fa-solid fa-spinner fa-spin text-4xl text-blue-500"></i><p class="mt-4 text-slate-600">جاري تحليل المخزون...</p></div></div>`;
        showModalContent(loadingHtml);
        // SIMULATE API CALL
        setTimeout(() => {
            const mockResponse = `[ {"itemName":"أرز بسمتي (10 كج)","currentQuantity":85,"reorderPoint":100,"suggestedQuantity":50,"reasoning":"المخزون أقل من الحد المطلوب، وهو سلعة أساسية ذات استهلاك عالي."}, {"itemName":"سكر أبيض (5 كج)","currentQuantity":45,"reorderPoint":70,"suggestedQuantity":60,"reasoning":"وصل المخزون إلى مستوى حرج. ينصح بزيادة الكمية لتغطية الطلب المتوقع."}, {"itemName":"تونة (علبة)","currentQuantity":98,"reorderPoint":400,"suggestedQuantity":500,"reasoning":"نقص كبير جداً في المخزون."} ]`;
            const suggestions = JSON.parse(mockResponse);
            displayInventorySuggestions(suggestions);
        }, 2000);
    }
    
    function displayInventorySuggestions(suggestions) {
        let resultsHtml = `<div class="p-5 border-b flex justify-between items-center"><h3 class="text-2xl font-bold text-slate-800 flex items-center gap-3"><i class="fa-solid fa-lightbulb text-blue-500"></i><span>توصيات المخزون</span></h3><button id="close-modal-btn" class="text-slate-400 hover:text-slate-700"><i class="fa-solid fa-times text-2xl"></i></button></div><div class="p-6 max-h-[60vh] overflow-y-auto"><div class="space-y-4">`;
        suggestions.forEach(item => { resultsHtml += `<div class="bg-slate-100 p-4 rounded-lg border-r-4 border-blue-500"><h4 class="font-bold text-lg text-slate-800">${item.itemName}</h4><p class="mt-2 text-sm text-slate-800"><strong class="text-blue-700">التوصية:</strong> طلب <strong class="text-blue-700">${item.suggestedQuantity}</strong> وحدة. <strong>السبب:</strong> ${item.reasoning}</p></div>`; });
        resultsHtml += `</div></div><div class="px-6 py-4 bg-slate-50 border-t rounded-b-xl text-xs text-slate-500"><i class="fa-solid fa-info-circle"></i> تم الإنشاء بواسطة Gemini.</div>`;
        showModalContent(resultsHtml);
    }

    // Gemini Feature 2: Suggest New Suppliers
    function handleSuggestNewSuppliers() {
        let formHtml = `<div class="p-5 border-b flex justify-between items-center"><h3 class="text-2xl font-bold text-slate-800 flex items-center gap-3"><i class="fa-solid fa-wand-magic-sparkles text-purple-500"></i><span>اقتراح موردين جدد</span></h3><button id="close-modal-btn" class="text-slate-400 hover:text-slate-700"><i class="fa-solid fa-times text-2xl"></i></button></div>
                        <div class="p-6">
                            <form id="suggest-supplier-form">
                                <label for="product-type" class="block text-sm font-medium text-gray-700 mb-2">ما هو نوع المنتج الذي تبحث عن مورد له؟</label>
                                <input type="text" id="product-type" placeholder="مثال: أجبان عالية الجودة، مواد تنظيف..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500" required>
                                <button type="submit" class="w-full mt-4 bg-purple-600 hover:bg-purple-700 text-white font-bold py-2.5 px-4 rounded-lg flex items-center justify-center gap-2">
                                    <i id="suggest-icon" class="fa-solid fa-search"></i><span id="suggest-text">ابحث عن موردين</span>
                                </button>
                            </form>
                        </div>`;
        showModalContent(formHtml);
        document.getElementById('suggest-supplier-form').addEventListener('submit', (e) => {
            e.preventDefault();
            const productType = document.getElementById('product-type').value;
            const btnText = document.getElementById('suggest-text');
            const btnIcon = document.getElementById('suggest-icon');
            btnText.innerText = 'جاري البحث...';
            btnIcon.className = 'fa-solid fa-spinner fa-spin';
            document.querySelector('#suggest-supplier-form button').disabled = true;

            // SIMULATE API CALL
            setTimeout(() => {
                const mockResponse = `[ {"suggestedSupplier":"شركة الألبان الكويتية الدنماركية (KDD)","specialty":"منتجات الألبان والأجبان","reason":"شركة رائدة في السوق الكويتي ومعروفة بجودة منتجاتها العالية وتنوعها الكبير."}, {"suggestedSupplier":"الشركة الكويتية للأغذية (أمريكانا)","specialty":"منتجات غذائية متنوعة","reason":"تمتلك شبكة توزيع واسعة وتقدم مجموعة كبيرة من الأجبان المستوردة والمصنعة محلياً."}, {"suggestedSupplier":"شركة الخليج للتموين","specialty":"مستورد وموزع للمواد الغذائية","reason":"وكيل للعديد من العلامات التجارية العالمية للأجبان الفاخرة، خيار ممتاز للمنتجات المتخصصة."} ]`;
                const suggestions = JSON.parse(mockResponse);
                displaySupplierSuggestions(suggestions, productType);
            }, 2500);
        });
    }

    function displaySupplierSuggestions(suggestions, productType) {
        let resultsHtml = `<div class="p-5 border-b flex justify-between items-center"><h3 class="text-2xl font-bold text-slate-800 flex items-center gap-3"><i class="fa-solid fa-wand-magic-sparkles text-purple-500"></i><span>موردون مقترحون لـ "${productType}"</span></h3><button id="close-modal-btn" class="text-slate-400 hover:text-slate-700"><i class="fa-solid fa-times text-2xl"></i></button></div><div class="p-6 max-h-[60vh] overflow-y-auto"><div class="space-y-4">`;
        suggestions.forEach(item => {
            resultsHtml += `<div class="bg-slate-100 p-4 rounded-lg border-r-4 border-purple-500"><h4 class="font-bold text-lg text-slate-800">${item.suggestedSupplier}</h4><p class="text-sm font-medium text-purple-800 bg-purple-100 rounded-full inline-block px-3 py-1 mt-2">${item.specialty}</p><p class="mt-2 text-sm text-slate-700">${item.reason}</p></div>`;
        });
        resultsHtml += `</div></div><div class="px-6 py-4 bg-slate-50 border-t rounded-b-xl text-xs text-slate-500"><i class="fa-solid fa-info-circle"></i> تم الإنشاء بواسطة Gemini.</div>`;
        showModalContent(resultsHtml);
    }

    // --- Initial Load ---
    document.addEventListener('DOMContentLoaded', () => {
        const dashboardLink = document.getElementById('dashboard-link');
        setActiveLink(dashboardLink);
        renderDashboard(); 
    });

</script>

</body>
</html>
